name: 📊 Code Quality & Performance

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Chạy vào 3:00 AM UTC mỗi ngày
    - cron: '0 3 * * *'

permissions:
  contents: read
  security-events: write
  actions: read
  pull-requests: write

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Job 1: Code quality analysis
  code-quality:
    name: 📊 Code Quality Analysis
    runs-on: ubuntu-latest

    services:
      mongodb:
        image: mongo:6.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password123
          MONGO_INITDB_DATABASE: qltime_test
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Cần full history cho SonarCloud
          
      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          
      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: 🧪 Run tests with coverage
        run: pnpm --filter backend test:cov
        env:
          MONGODB_URI: ************************************************************************
          JWT_SECRET: test_jwt_secret
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}

      - name: 📊 SonarCloud Scan
        if: ${{ secrets.SONAR_TOKEN != '' }}
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        continue-on-error: true

      - name: ⚠️ SonarCloud Scan Skipped
        if: ${{ secrets.SONAR_TOKEN == '' }}
        run: |
          echo "⚠️ SonarCloud scan skipped - SONAR_TOKEN not configured"
          echo "To enable SonarCloud analysis:"
          echo "1. Sign up at https://sonarcloud.io"
          echo "2. Import your repository"
          echo "3. Get your token and add it to GitHub Secrets as SONAR_TOKEN"
          
      - name: 📤 Upload coverage to Codecov
        if: ${{ secrets.CODECOV_TOKEN != '' }}
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage
          token: ${{ secrets.CODECOV_TOKEN }}
        continue-on-error: true

      - name: ⚠️ Codecov Upload Skipped
        if: ${{ secrets.CODECOV_TOKEN == '' }}
        run: |
          echo "⚠️ Codecov upload skipped - CODECOV_TOKEN not configured"
          echo "To enable Codecov integration:"
          echo "1. Sign up at https://codecov.io"
          echo "2. Import your repository"
          echo "3. Get your token and add it to GitHub Secrets as CODECOV_TOKEN"
          
      - name: 📊 Code complexity analysis
        run: |
          echo "📊 Analyzing code complexity..."

          # Install complexity analysis tools
          npm install -g complexity-report typescript-analyzer || true

          # Generate complexity report
          echo "📊 Code Complexity Report" > complexity-report.txt
          echo "=========================" >> complexity-report.txt
          echo "Generated: $(date)" >> complexity-report.txt
          echo "" >> complexity-report.txt

          # Analyze backend complexity
          echo "🔧 Backend Analysis:" >> complexity-report.txt
          echo "-------------------" >> complexity-report.txt

          if [ -d "backend/src" ]; then
            # Count files and lines
            echo "📁 Total TypeScript files: $(find backend/src -name "*.ts" | wc -l)" >> complexity-report.txt
            echo "📄 Total lines of code: $(find backend/src -name "*.ts" -exec wc -l {} + | tail -1 | awk '{print $1}')" >> complexity-report.txt
            echo "" >> complexity-report.txt

            # Find largest files
            echo "📈 Largest files:" >> complexity-report.txt
            find backend/src -name "*.ts" -exec wc -l {} + | sort -nr | head -5 >> complexity-report.txt
            echo "" >> complexity-report.txt
          fi

          # Analyze frontend complexity
          echo "🌐 Frontend Analysis:" >> complexity-report.txt
          echo "--------------------" >> complexity-report.txt

          if [ -d "frontend/src" ]; then
            echo "📁 Total React files: $(find frontend/src -name "*.tsx" -o -name "*.ts" | wc -l)" >> complexity-report.txt
            echo "📄 Total lines of code: $(find frontend/src -name "*.tsx" -o -name "*.ts" -exec wc -l {} + | tail -1 | awk '{print $1}')" >> complexity-report.txt
            echo "" >> complexity-report.txt

            # Find largest components
            echo "📈 Largest components:" >> complexity-report.txt
            find frontend/src -name "*.tsx" -exec wc -l {} + | sort -nr | head -5 >> complexity-report.txt
          fi

      - name: 📤 Upload complexity report
        uses: actions/upload-artifact@v4
        with:
          name: complexity-report
          path: complexity-report.txt
          retention-days: 30

  # Job 2: Performance testing
  performance-test:
    name: ⚡ Performance Testing
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:6.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password123
          MONGO_INITDB_DATABASE: qltime_test
        ports:
          - 27017:27017
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          
      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: 🏗️ Build backend
        run: pnpm --filter backend build
        
      - name: 🚀 Start backend for testing
        run: |
          cd backend
          pnpm start:prod &
          sleep 10
        env:
          MONGODB_URI: ************************************************************************
          JWT_SECRET: test_jwt_secret
          PORT: 3001
          
      - name: ⚡ Run API performance tests
        run: |
          echo "⚡ Running API performance tests..."
          npx artillery quick --count 10 --num 5 http://localhost:3001/health
          
      - name: 📊 Generate performance report
        run: |
          echo "📊 Generating performance report..."
          npx artillery run --output performance-report.json performance-test.yml || true
          
      - name: 📤 Upload performance report
        uses: actions/upload-artifact@v4
        with:
          name: performance-report
          path: performance-report.json
          retention-days: 30

  # Job 3: Frontend performance audit
  frontend-audit:
    name: 🌐 Frontend Performance Audit
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🔍 Find available port
        run: |
          echo "🔍 Finding available port..."
          # Find an available port starting from 3000
          for port in {3000..3010}; do
            if ! lsof -ti:$port >/dev/null 2>&1; then
              echo "✅ Port $port is available"
              echo "FRONTEND_PORT=$port" >> $GITHUB_ENV
              break
            fi
          done

          if [ -z "$FRONTEND_PORT" ]; then
            echo "❌ No available ports found"
            exit 1
          fi

      - name: 🏗️ Build frontend
        run: pnpm --filter frontend build
        env:
          NEXT_PUBLIC_API_URL: http://localhost:3001

      - name: 🚀 Start frontend server
        run: |
          cd frontend
          echo "🚀 Starting Next.js server on port $FRONTEND_PORT..."
          PORT=$FRONTEND_PORT pnpm start &
          SERVER_PID=$!
          echo "SERVER_PID=$SERVER_PID" >> $GITHUB_ENV

          # Wait for server to be ready
          echo "⏳ Waiting for server to be ready..."
          for i in {1..30}; do
            if curl -f http://localhost:$FRONTEND_PORT >/dev/null 2>&1; then
              echo "✅ Server is ready on port $FRONTEND_PORT!"
              break
            else
              echo "⏳ Waiting... (attempt $i/30)"
              sleep 2
            fi
            if [ $i -eq 30 ]; then
              echo "❌ Server failed to start within timeout"
              exit 1
            fi
          done

      - name: 🔍 Run Lighthouse audit
        run: |
          echo "🔍 Running Lighthouse audit on http://localhost:$FRONTEND_PORT..."

          # Install Lighthouse CLI
          npm install -g @lhci/cli@0.12.x

          # Create dynamic config
          cat > .lighthouserc-dynamic.json << EOF
          {
            "ci": {
              "collect": {
                "url": ["http://localhost:$FRONTEND_PORT"],
                "numberOfRuns": 3
              },
              "assert": {
                "assertions": {
                  "categories:performance": ["warn", {"minScore": 0.6}],
                  "categories:accessibility": ["warn", {"minScore": 0.7}],
                  "categories:best-practices": ["warn", {"minScore": 0.6}],
                  "categories:seo": ["warn", {"minScore": 0.6}]
                }
              },
              "upload": {
                "target": "temporary-public-storage"
              }
            }
          }
          EOF

          # Run Lighthouse
          lhci collect --config=.lighthouserc-dynamic.json || true
          lhci assert --config=.lighthouserc-dynamic.json || true
          lhci upload --config=.lighthouserc-dynamic.json || true

      - name: 🛑 Stop frontend server
        if: always()
        run: |
          if [ ! -z "$SERVER_PID" ]; then
            echo "🛑 Stopping server (PID: $SERVER_PID)..."
            kill $SERVER_PID || true
            sleep 2
          fi
          # Kill any remaining processes on the port
          if [ ! -z "$FRONTEND_PORT" ]; then
            lsof -ti:$FRONTEND_PORT | xargs -r kill -9 || true
          fi

      - name: 📊 Bundle size analysis
        run: |
          echo "📊 Analyzing bundle size..."
          cd frontend

          # Generate bundle analysis
          echo "📦 Frontend bundle analysis:" > bundle-analysis.txt
          echo "================================" >> bundle-analysis.txt
          echo "Build date: $(date)" >> bundle-analysis.txt
          echo "" >> bundle-analysis.txt

          if [ -d ".next" ]; then
            echo "📁 .next directory size:" >> bundle-analysis.txt
            du -sh .next >> bundle-analysis.txt
            echo "" >> bundle-analysis.txt

            echo "📄 JavaScript bundles:" >> bundle-analysis.txt
            find .next/static -name "*.js" | head -10 | xargs ls -lh >> bundle-analysis.txt || true
            echo "" >> bundle-analysis.txt

            echo "🎨 CSS files:" >> bundle-analysis.txt
            find .next/static -name "*.css" | head -5 | xargs ls -lh >> bundle-analysis.txt || true
            echo "" >> bundle-analysis.txt

            echo "🖼️ Static assets:" >> bundle-analysis.txt
            find .next/static -type f \( -name "*.png" -o -name "*.jpg" -o -name "*.svg" \) | head -5 | xargs ls -lh >> bundle-analysis.txt || true
          else
            echo "❌ .next directory not found" >> bundle-analysis.txt
          fi

      - name: 📤 Upload performance artifacts
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: frontend-performance-audit
          path: |
            frontend/bundle-analysis.txt
            .lighthouseci/
            *.json
          retention-days: 7

  # Job 4: Security analysis
  security-analysis:
    name: 🔒 Security Analysis
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🔒 Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: 🔒 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

      - name: 🔒 Run Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten
        continue-on-error: true

      - name: 🔒 Run npm audit
        run: |
          echo "🔒 Running npm security audit..."
          cd backend && pnpm audit --audit-level moderate || true
          cd ../frontend && pnpm audit --audit-level moderate || true

  # Job 5: Documentation check
  documentation-check:
    name: 📚 Documentation Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📚 Check README files
        run: |
          echo "📚 Checking documentation..."
          
          # Kiểm tra README files tồn tại
          if [ ! -f README.md ]; then
            echo "❌ Root README.md not found"
            exit 1
          fi
          
          if [ ! -f backend/README.md ]; then
            echo "❌ Backend README.md not found"
            exit 1
          fi
          
          if [ ! -f frontend/README.md ]; then
            echo "❌ Frontend README.md not found"
            exit 1
          fi
          
          echo "✅ All README files found"
          
      - name: 📚 Check API documentation
        run: |
          echo "📚 Checking API documentation..."
          # Kiểm tra Swagger/OpenAPI docs
          if grep -q "swagger" backend/src/main.ts; then
            echo "✅ Swagger documentation configured"
          else
            echo "⚠️ Swagger documentation not found"
          fi
          
      - name: 📚 Generate documentation report
        run: |
          echo "📚 Generating documentation report..."
          echo "# Documentation Report" > doc-report.md
          echo "" >> doc-report.md
          echo "## Files checked:" >> doc-report.md
          echo "- ✅ Root README.md" >> doc-report.md
          echo "- ✅ Backend README.md" >> doc-report.md
          echo "- ✅ Frontend README.md" >> doc-report.md
          echo "- ✅ API Documentation (Swagger)" >> doc-report.md
          
      - name: 📤 Upload documentation report
        uses: actions/upload-artifact@v4
        with:
          name: documentation-report
          path: doc-report.md
          retention-days: 30
