# SonarCloud configuration
sonar.projectKey=hungtvu113_WebsiteTimE
sonar.organization=hungtvu113

# Project information
sonar.projectName=WebsiteTimE
sonar.projectVersion=1.0.0

# Source code
sonar.sources=backend/src,frontend/src
sonar.exclusions=**/node_modules/**,**/dist/**,**/.next/**,**/coverage/**,**/*.spec.ts,**/*.test.ts

# Test coverage
sonar.javascript.lcov.reportPaths=backend/coverage/lcov.info
sonar.testExecutionReportPaths=backend/coverage/test-reporter.xml

# Language settings
sonar.typescript.node=node_modules/typescript/lib/typescript.js

# Backend specific
sonar.sources.backend=backend/src
sonar.tests.backend=backend/test

# Frontend specific  
sonar.sources.frontend=frontend/src
sonar.exclusions.frontend=frontend/.next/**,frontend/out/**
