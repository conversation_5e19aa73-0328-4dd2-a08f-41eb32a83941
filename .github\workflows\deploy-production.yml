name: 🚀 Deploy to Production

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy'
        required: true
        default: 'latest'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Job 1: Validate release
  validate-release:
    name: ✅ Validate Release
    runs-on: ubuntu-latest
    
    outputs:
      version: ${{ steps.get-version.outputs.version }}
      
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🏷️ Get version
        id: get-version
        run: |
          if [ "${{ github.event_name }}" == "release" ]; then
            echo "version=${{ github.event.release.tag_name }}" >> $GITHUB_OUTPUT
          else
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          fi
          
      - name: ✅ Validate version format
        run: |
          VERSION="${{ steps.get-version.outputs.version }}"
          if [[ ! $VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]] && [[ $VERSION != "latest" ]]; then
            echo "❌ Invalid version format: $VERSION"
            echo "Expected format: v1.0.0 or 'latest'"
            exit 1
          fi
          echo "✅ Version format is valid: $VERSION"

  # Job 2: Run full test suite
  full-test-suite:
    name: 🧪 Full Test Suite
    runs-on: ubuntu-latest
    needs: [validate-release]
    
    services:
      mongodb:
        image: mongo:6.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password123
          MONGO_INITDB_DATABASE: qltime_test
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
          
      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: 🔍 Lint all packages
        run: pnpm lint
        
      - name: 🧪 Run all tests
        run: pnpm test
        env:
          MONGODB_URI: ************************************************************************
          JWT_SECRET: test_jwt_secret
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          
      - name: 🏗️ Build all packages
        run: pnpm build
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.PROD_API_URL }}
          NEXT_PUBLIC_GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}

  # Job 3: Build production Docker images
  build-production-images:
    name: 🐳 Build Production Images
    runs-on: ubuntu-latest
    needs: [validate-release, full-test-suite]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: 🔐 Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
          
      - name: 🏗️ Build and push backend production image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/websitetime-backend:${{ needs.validate-release.outputs.version }}
            ${{ secrets.DOCKER_USERNAME }}/websitetime-backend:production
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: 🏗️ Build and push frontend production image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/websitetime-frontend:${{ needs.validate-release.outputs.version }}
            ${{ secrets.DOCKER_USERNAME }}/websitetime-frontend:production
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Job 4: Security scan for production
  production-security-scan:
    name: 🔒 Production Security Scan
    runs-on: ubuntu-latest
    needs: [build-production-images, validate-release]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔒 Scan backend production image
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ secrets.DOCKER_USERNAME }}/websitetime-backend:${{ needs.validate-release.outputs.version }}
          format: 'table'
          exit-code: '1'
          severity: 'CRITICAL,HIGH'
          
      - name: 🔒 Scan frontend production image
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ secrets.DOCKER_USERNAME }}/websitetime-frontend:${{ needs.validate-release.outputs.version }}
          format: 'table'
          exit-code: '1'
          severity: 'CRITICAL,HIGH'

  # Job 5: Deploy to production
  deploy-production:
    name: 🚀 Deploy to Production
    runs-on: ubuntu-latest
    needs: [validate-release, build-production-images, production-security-scan]
    environment: production
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🚀 Deploy to production
        run: |
          echo "🚀 Deploying version ${{ needs.validate-release.outputs.version }} to production..."
          echo "Backend image: ${{ secrets.DOCKER_USERNAME }}/websitetime-backend:${{ needs.validate-release.outputs.version }}"
          echo "Frontend image: ${{ secrets.DOCKER_USERNAME }}/websitetime-frontend:${{ needs.validate-release.outputs.version }}"
          
          # Ví dụ deploy script (thay thế bằng logic deploy thực tế)
          # ssh user@production-server "docker-compose pull && docker-compose up -d"
          # kubectl set image deployment/backend backend=${{ secrets.DOCKER_USERNAME }}/websitetime-backend:${{ needs.validate-release.outputs.version }}
          # kubectl set image deployment/frontend frontend=${{ secrets.DOCKER_USERNAME }}/websitetime-frontend:${{ needs.validate-release.outputs.version }}
          
      - name: ✅ Deployment completed
        run: |
          echo "✅ Production deployment completed successfully!"
          echo "🌐 Application URL: ${{ secrets.PROD_APP_URL }}"
          
      - name: 📢 Notify deployment
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          text: |
            🚀 Production deployment ${{ job.status }}!
            Version: ${{ needs.validate-release.outputs.version }}
            URL: ${{ secrets.PROD_APP_URL }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Job 6: Post-deployment health check
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: [deploy-production, validate-release]
    
    steps:
      - name: 🏥 Check backend health
        run: |
          echo "🏥 Checking backend health..."
          for i in {1..10}; do
            if curl -f "${{ secrets.PROD_API_URL }}/health"; then
              echo "✅ Backend is healthy!"
              break
            else
              echo "⏳ Waiting for backend to be ready... (attempt $i/10)"
              sleep 30
            fi
            if [ $i -eq 10 ]; then
              echo "❌ Backend health check failed!"
              exit 1
            fi
          done
          
      - name: 🏥 Check frontend health
        run: |
          echo "🏥 Checking frontend health..."
          for i in {1..10}; do
            if curl -f "${{ secrets.PROD_APP_URL }}"; then
              echo "✅ Frontend is healthy!"
              break
            else
              echo "⏳ Waiting for frontend to be ready... (attempt $i/10)"
              sleep 30
            fi
            if [ $i -eq 10 ]; then
              echo "❌ Frontend health check failed!"
              exit 1
            fi
          done
          
      - name: ✅ All health checks passed
        run: echo "✅ All services are healthy and running!"
